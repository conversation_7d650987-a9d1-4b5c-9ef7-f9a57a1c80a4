#!/bin/bash

# =============================================================================
# 备份原有Celery脚本
# =============================================================================

echo "正在备份原有的Celery管理脚本..."

# 创建备份目录
mkdir -p scripts/celery/backup_$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="scripts/celery/backup_$(date +%Y%m%d_%H%M%S)"

# 备份原有脚本
for script in start_celery.sh start_celery_dev.sh start_celery_prod.sh stop_celery.sh stop_celery_dev.sh stop_celery_prod.sh; do
    if [ -f "scripts/celery/$script" ]; then
        cp "scripts/celery/$script" "$BACKUP_DIR/"
        echo "✅ 已备份: $script"
    else
        echo "⚠️  文件不存在: $script"
    fi
done

echo "备份完成，备份目录: $BACKUP_DIR"
echo ""
echo "现在可以安全地使用新的统一管理脚本："
echo "  ./scripts/celery/celery_manager.sh dev start"
echo "  ./scripts/celery/celery_manager.sh prod stop"
echo "  ./scripts/celery/celery_manager.sh dev restart"
