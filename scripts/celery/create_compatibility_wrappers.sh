#!/bin/bash

# =============================================================================
# 创建向后兼容的包装脚本
# =============================================================================

echo "正在创建向后兼容的包装脚本..."

# 创建start_celery_dev.sh包装脚本
cat > scripts/celery/start_celery_dev.sh << 'EOF'
#!/bin/bash
# 向后兼容包装脚本 - 调用统一管理脚本
echo "⚠️  此脚本已被统一管理脚本替代"
echo "正在调用: ./scripts/celery/celery_manager.sh dev start"
echo ""
exec ./scripts/celery/celery_manager.sh dev start "$@"
EOF

# 创建start_celery_prod.sh包装脚本
cat > scripts/celery/start_celery_prod.sh << 'EOF'
#!/bin/bash
# 向后兼容包装脚本 - 调用统一管理脚本
echo "⚠️  此脚本已被统一管理脚本替代"
echo "正在调用: ./scripts/celery/celery_manager.sh prod start"
echo ""
exec ./scripts/celery/celery_manager.sh prod start "$@"
EOF

# 创建stop_celery_dev.sh包装脚本
cat > scripts/celery/stop_celery_dev.sh << 'EOF'
#!/bin/bash
# 向后兼容包装脚本 - 调用统一管理脚本
echo "⚠️  此脚本已被统一管理脚本替代"
echo "正在调用: ./scripts/celery/celery_manager.sh dev stop"
echo ""
exec ./scripts/celery/celery_manager.sh dev stop "$@"
EOF

# 创建stop_celery_prod.sh包装脚本
cat > scripts/celery/stop_celery_prod.sh << 'EOF'
#!/bin/bash
# 向后兼容包装脚本 - 调用统一管理脚本
echo "⚠️  此脚本已被统一管理脚本替代"
echo "正在调用: ./scripts/celery/celery_manager.sh prod stop"
echo ""
exec ./scripts/celery/celery_manager.sh prod stop "$@"
EOF

# 创建start_celery.sh包装脚本（通用版本，默认开发环境）
cat > scripts/celery/start_celery.sh << 'EOF'
#!/bin/bash
# 向后兼容包装脚本 - 调用统一管理脚本
echo "⚠️  此脚本已被统一管理脚本替代"
echo "正在调用: ./scripts/celery/celery_manager.sh dev start (默认开发环境)"
echo ""
exec ./scripts/celery/celery_manager.sh dev start "$@"
EOF

# 创建stop_celery.sh包装脚本（通用版本，默认开发环境）
cat > scripts/celery/stop_celery.sh << 'EOF'
#!/bin/bash
# 向后兼容包装脚本 - 调用统一管理脚本
echo "⚠️  此脚本已被统一管理脚本替代"
echo "正在调用: ./scripts/celery/celery_manager.sh dev stop (默认开发环境)"
echo ""
exec ./scripts/celery/celery_manager.sh dev stop "$@"
EOF

# 设置执行权限
chmod +x scripts/celery/start_celery*.sh
chmod +x scripts/celery/stop_celery*.sh

echo "✅ 向后兼容包装脚本创建完成"
echo ""
echo "现在所有原有脚本都会自动调用新的统一管理脚本："
echo "  start_celery_dev.sh   -> celery_manager.sh dev start"
echo "  start_celery_prod.sh  -> celery_manager.sh prod start"
echo "  stop_celery_dev.sh    -> celery_manager.sh dev stop"
echo "  stop_celery_prod.sh   -> celery_manager.sh prod stop"
echo "  start_celery.sh       -> celery_manager.sh dev start"
echo "  stop_celery.sh        -> celery_manager.sh dev stop"
