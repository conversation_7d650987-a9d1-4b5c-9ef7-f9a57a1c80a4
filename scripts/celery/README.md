# AiLabel平台Celery管理脚本

## 📋 概述

本目录包含AiLabel平台的Celery服务管理脚本，提供统一的开发环境和生产环境管理功能。

## 🚀 统一管理脚本

### `celery_manager.sh` - 主要管理脚本

这是新的统一Celery管理脚本，整合了原有的6个独立脚本的所有功能。

#### 使用方法

```bash
./scripts/celery/celery_manager.sh <环境> <操作> [选项]
```

#### 参数说明

- **环境** (必需):
  - `dev` - 开发环境
  - `prod` - 生产环境

- **操作** (必需):
  - `start` - 启动Celery服务
  - `stop` - 停止Celery服务
  - `restart` - 重启Celery服务

- **选项** (可选):
  - `--help, -h` - 显示帮助信息
  - `--force` - 强制执行操作（跳过生产环境确认）
  - `--verbose` - 详细输出模式

#### 使用示例

```bash
# 启动开发环境
./scripts/celery/celery_manager.sh dev start

# 停止生产环境
./scripts/celery/celery_manager.sh prod stop

# 重启开发环境
./scripts/celery/celery_manager.sh dev restart

# 强制启动生产环境（跳过确认）
./scripts/celery/celery_manager.sh prod start --force

# 查看帮助信息
./scripts/celery/celery_manager.sh --help
```

## 🔧 环境配置

### 开发环境 (dev)
- **Conda环境**: `ailabel_env`
- **日志目录**: `log/dev`
- **配置文件**: `.env.dev`
- **进程标识**: `aiLabel_dev`
- **等待时间**: 10秒

### 生产环境 (prod)
- **Conda环境**: `/home/<USER>/miniconda3/envs/myenv`
- **日志目录**: `log/prod`
- **配置文件**: `.env.prod`
- **进程标识**: `aiLabel_prod`
- **等待时间**: 15秒

## 📁 文件结构

```
scripts/celery/
├── celery_manager.sh              # 统一管理脚本 (主要)
├── start_celery_dev.sh           # 向后兼容包装脚本
├── start_celery_prod.sh          # 向后兼容包装脚本
├── stop_celery_dev.sh            # 向后兼容包装脚本
├── stop_celery_prod.sh           # 向后兼容包装脚本
├── start_celery.sh               # 向后兼容包装脚本
├── stop_celery.sh                # 向后兼容包装脚本
├── backup_original_scripts.sh    # 备份原有脚本工具
├── create_compatibility_wrappers.sh  # 创建兼容包装脚本工具
└── README.md                     # 本文档
```

## 🔄 向后兼容性

为了保持向后兼容性，原有的6个脚本已被转换为包装脚本，它们会自动调用新的统一管理脚本：

- `start_celery_dev.sh` → `celery_manager.sh dev start`
- `start_celery_prod.sh` → `celery_manager.sh prod start`
- `stop_celery_dev.sh` → `celery_manager.sh dev stop`
- `stop_celery_prod.sh` → `celery_manager.sh prod stop`
- `start_celery.sh` → `celery_manager.sh dev start` (默认开发环境)
- `stop_celery.sh` → `celery_manager.sh dev stop` (默认开发环境)

## ✨ 新功能特性

### 1. 统一管理
- 单一脚本管理所有环境和操作
- 一致的命令行接口
- 统一的错误处理和日志输出

### 2. 安全性增强
- 生产环境操作需要确认（可用--force跳过）
- 进程隔离和安全检查
- 用户权限验证

### 3. 日志管理
- 彩色日志输出
- 会话标记和时间戳
- 自动日志备份

### 4. 进程管理
- 智能进程识别和清理
- 优雅停止和强制终止
- 残留进程检测

### 5. 环境隔离
- 严格的环境配置分离
- 独立的日志目录
- 不同的进程标识

## 🛠️ 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x scripts/celery/celery_manager.sh
   ```

2. **环境配置文件不存在**
   - 确保 `.env.dev` 或 `.env.prod` 文件存在
   - 检查文件路径和权限

3. **Conda环境问题**
   - 确保指定的conda环境已创建
   - 检查conda配置路径

4. **进程清理问题**
   - 使用 `ps aux | grep celery` 查看进程
   - 手动清理残留进程：`pkill -f "celery.*ailabel_env"`

### 日志查看

```bash
# 查看实时日志
tail -f log/dev/celery.log    # 开发环境
tail -f log/prod/celery.log   # 生产环境

# 查看历史日志
ls -la log/dev/               # 开发环境日志文件
ls -la log/prod/              # 生产环境日志文件
```

## 📝 更新日志

### v1.0.0 (2025-07-14)
- 创建统一的Celery管理脚本
- 整合原有6个独立脚本功能
- 添加向后兼容性支持
- 增强安全性和错误处理
- 改进日志管理和进程控制

## 🤝 贡献

如需修改或扩展功能，请：
1. 在 `celery_manager.sh` 中进行修改
2. 确保向后兼容性
3. 更新本文档
4. 进行充分测试

## 📞 支持

如有问题，请联系AiLabel开发团队或查看项目文档。
