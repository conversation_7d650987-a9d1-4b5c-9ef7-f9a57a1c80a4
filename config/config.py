from datetime import timedelta
from utils.gpu import seek_gpu
from collections import defaultdict
import sys
import os
from dotenv import load_dotenv

# 加载环境变量
# 根据环境变量 CELERY_ENV 决定加载哪个配置文件
# 如果没有设置 CELERY_ENV，则优先加载 .env.prod（生产环境）
celery_env = os.getenv('CELERY_ENV', 'prod')

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

if celery_env == 'dev' and os.path.exists(os.path.join(project_root, '.env.dev')):
    load_dotenv(os.path.join(project_root, '.env.dev'))
    print("🎯 已加载开发环境配置 (.env.dev)")
elif celery_env == 'prod' and os.path.exists(os.path.join(project_root, '.env.prod')):
    load_dotenv(os.path.join(project_root, '.env.prod'))
    print("🎯 已加载生产环境配置 (.env.prod)")
elif os.path.exists(os.path.join(project_root, '.env.prod')):
    load_dotenv(os.path.join(project_root, '.env.prod'))
    print("🎯 已加载生产环境配置 (.env.prod) [默认]")
elif os.path.exists(os.path.join(project_root, '.env.dev')):
    load_dotenv(os.path.join(project_root, '.env.dev'))
    print("🎯 已加载开发环境配置 (.env.dev) [备用]")
else:
    print("⚠️ 未找到环境配置文件，使用默认配置")

# 移除HOST_UPLOADS配置 - 前端相关配置不再需要

# 移除MySQL数据库配置 - 数据库操作由Java后端负责

# Debug Settings
DEBUG_FLAG = os.getenv('DEBUG_FLAG', 'False').lower() == 'true'

# 设置GPU
seek_gpu()

# RabbitMQ 连接信息
RABBITMQ_HOST = os.getenv('RABBITMQ_HOST', '**************')
RABBITMQ_PORT = int(os.getenv('RABBITMQ_PORT', '25675'))
RABBITMQ_ACCOUNT = os.getenv('RABBITMQ_ACCOUNT', 'admin')
RABBITMQ_PASSWORD = os.getenv('RABBITMQ_PASSWORD', 'vipa@404')

# RabbitMQ 连接配置
RABBITMQ_CONNECTION_TIMEOUT = int(os.getenv('RABBITMQ_CONNECTION_TIMEOUT', '60'))
RABBITMQ_HEARTBEAT = int(os.getenv('RABBITMQ_HEARTBEAT', '300'))
RABBITMQ_SOCKET_TIMEOUT = int(os.getenv('RABBITMQ_SOCKET_TIMEOUT', '10'))
RABBITMQ_BLOCKED_CONNECTION_TIMEOUT = int(os.getenv('RABBITMQ_BLOCKED_CONNECTION_TIMEOUT', '300'))
RABBITMQ_CONNECTION_ATTEMPTS = int(os.getenv('RABBITMQ_CONNECTION_ATTEMPTS', '3'))
RABBITMQ_RETRY_DELAY = int(os.getenv('RABBITMQ_RETRY_DELAY', '1'))

# Redis 连接信息
REDIS_HOST = os.getenv('REDIS_HOST', '**************')
REDIS_PORT = int(os.getenv('REDIS_PORT', '26379'))  # 默认使用生产环境端口

# 图片保存路径 - 优先使用环境变量，默认使用生产环境路径
PROJECT_SAVE_DIR = os.getenv('PROJECT_SAVE_DIR', '/nfs5/medlabel/medlabel_212/projects')

print(f"🎯 当前配置:")
print(f"   环境: {'生产' if not DEBUG_FLAG else '开发'}")
print(f"   Redis: {REDIS_HOST}:{REDIS_PORT}")
print(f"   RabbitMQ: {RABBITMQ_HOST}:{RABBITMQ_PORT}")
print(f"   项目保存目录: {PROJECT_SAVE_DIR}")
