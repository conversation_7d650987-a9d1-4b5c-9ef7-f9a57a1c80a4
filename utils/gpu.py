"""
GPU设备选择工具

从asm.utils.gpu迁移而来，提供GPU设备选择功能
"""

import os
import logging

logger = logging.getLogger('app.gpu')

def seek_gpu():
    """
    选择合适的GPU设备
    
    当前实现：
    - 固定选择GPU 1
    - 设置CUDA_VISIBLE_DEVICES环境变量
    - 显示GPU内存信息
    
    注意：需要安装pynvml依赖
    """
    try:
        import pynvml
        
        choose_gid = 0
        max_free_memory = 0
        pynvml.nvmlInit()
        GPUCount = pynvml.nvmlDeviceGetCount()
        gid = 1
        handle = pynvml.nvmlDeviceGetHandleByIndex(gid)    # GPU的id
        meminfo = pynvml.nvmlDeviceGetMemoryInfo(handle)
        max_free_memory = meminfo.free / 1024**3
        
        # 注释掉的代码：原本用于选择空闲内存最大的GPU
        # for gid in range(GPUCount):
        #     handle = pynvml.nvmlDeviceGetHandleByIndex(gid)    # GPU的id
        #     meminfo = pynvml.nvmlDeviceGetMemoryInfo(handle)
        #     if max_free_memory < meminfo.free / 1024**3:
        #         max_free_memory = meminfo.free / 1024**3
        #         choose_gid = gid
        
        print('选择 GPU {}, 空置显存 {:.2f} GB'.format(gid, max_free_memory))
        logger.info('选择 GPU {}, 空置显存 {:.2f} GB'.format(gid, max_free_memory))
        os.environ["CUDA_VISIBLE_DEVICES"] = str(choose_gid)
        pynvml.nvmlShutdown()
        
    except ImportError:
        logger.warning("pynvml未安装，跳过GPU选择")
        print("⚠️ pynvml未安装，跳过GPU选择")
    except Exception as e:
        logger.error(f"GPU选择失败: {e}")
        print(f"⚠️ GPU选择失败: {e}")
